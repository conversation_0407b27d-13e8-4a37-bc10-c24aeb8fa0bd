import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

export class ThresholdStrategy extends TradingStrategy {
	getName(): string {
		return 'Threshold Strategy'
	}

	getDescription(): string {
		return 'Trades when price change exceeds a specified threshold percentage'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		const threshold = this.config.threshold || 0.02

		// Check if price change exceeds threshold
		if (Math.abs(priceData.changePercent) >= threshold) {
			const direction = priceData.trend === 'up' ? 'high' : 'low'
			const confidence = Math.min(Math.abs(priceData.changePercent) / threshold, 1)
			
			return {
				shouldTrade: true,
				direction,
				confidence,
				reason: `Price change ${priceData.changePercent.toFixed(2)}% exceeds threshold ${threshold}%`
			}
		}

		return {
			shouldTrade: false,
			reason: `Price change ${priceData.changePercent.toFixed(2)}% below threshold ${threshold}%`
		}
	}
}
