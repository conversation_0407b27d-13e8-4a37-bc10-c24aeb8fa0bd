import { StrategyType, StrategyConfig } from '../../../../shared/types'
import { TradingStrategy } from './TradingStrategy'
import { ThresholdStrategy } from './ThresholdStrategy'
import { MovingAverageStrategy } from './MovingAverageStrategy'
import { RSIStrategy } from './RSIStrategy'
import { MACDStrategy } from './MACDStrategy'
import { BollingerBandsStrategy } from './BollingerBandsStrategy'
import { CustomStrategy } from './CustomStrategy'

export class StrategyFactory {
	static createStrategy(type: StrategyType, config: StrategyConfig): TradingStrategy {
		switch (type) {
			case 'threshold':
				return new ThresholdStrategy(config)
			case 'moving-average':
				return new MovingAverageStrategy(config)
			case 'rsi':
				return new RSIStrategy(config)
			case 'macd':
				return new MACDStrategy(config)
			case 'bollinger-bands':
				return new BollingerBandsStrategy(config)
			case 'custom':
				return new CustomStrategy(config)
			default:
				throw new Error(`Unknown strategy type: ${type}`)
		}
	}

	static getAvailableStrategies(): Array<{ type: StrategyType; name: string; description: string }> {
		return [
			{
				type: 'threshold',
				name: 'Threshold Strategy',
				description: 'Trades when price change exceeds a specified threshold percentage'
			},
			{
				type: 'moving-average',
				name: 'Moving Average Crossover',
				description: 'Trades based on short-term and long-term moving average crossovers'
			},
			{
				type: 'rsi',
				name: 'RSI Strategy',
				description: 'Trades based on Relative Strength Index (RSI) overbought/oversold levels'
			},
			{
				type: 'macd',
				name: 'MACD Strategy',
				description: 'Trades based on MACD (Moving Average Convergence Divergence) signals'
			},
			{
				type: 'bollinger-bands',
				name: 'Bollinger Bands Strategy',
				description: 'Trades based on Bollinger Bands breakouts and mean reversion'
			},
			{
				type: 'custom',
				name: 'Custom Strategy',
				description: 'User-defined custom trading strategy with editable code'
			}
		]
	}

	static getDefaultConfig(type: StrategyType): StrategyConfig {
		switch (type) {
			case 'threshold':
				return { threshold: 0.02 }
			case 'moving-average':
				return { shortPeriod: 5, longPeriod: 20 }
			case 'rsi':
				return { rsiPeriod: 14, oversoldLevel: 30, overboughtLevel: 70 }
			case 'macd':
				return { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }
			case 'bollinger-bands':
				return { period: 20, standardDeviations: 2 }
			case 'custom':
				return { customCode: '' }
			default:
				return {}
		}
	}
}
