/// <reference types="vite/client" />
import type { BotStatus, TradeResult, TradingSettings, StopLossConfig } from '../../shared/types'

interface BrowserStatus {
	initialized: boolean
	loggedIn: boolean
}

interface ApiResponse {
	success: boolean
	message: string
}

interface LoginResponse extends ApiResponse {
	loggedIn: boolean
}

declare global {
	interface Window {
		electronAPI: {
			getSettings: () => Promise<TradingSettings>
			saveSettings: (settings: TradingSettings) => Promise<boolean>
			startBot: (settings: TradingSettings) => Promise<ApiResponse>
			stopBot: () => Promise<ApiResponse>
			getBotStatus: () => Promise<BotStatus>
			updateBotSettings: (settings: TradingSettings) => Promise<ApiResponse>
			initializeBrowser: () => Promise<ApiResponse>
			checkLoginStatus: () => Promise<LoginResponse>
			closeBrowser: () => Promise<ApiResponse>
			getBrowserStatus: () => Promise<BrowserStatus>
			onBotStatusUpdate: (callback: (status: BotStatus) => void) => void
			onPriceUpdate: (callback: (price: number) => void) => void
			onTradeResult: (callback: (result: TradeResult) => void) => void
			onStopLossTriggered: (callback: (data: { totalProfit: number; stopLossConfig?: StopLossConfig }) => void) => void
			onBotStatusMessage: (callback: (message: string) => void) => void
			showErrorDialog: (title: string, content: string) => void
			showInfoDialog: (title: string, content: string) => void
			removeAllListeners: (channel: string) => void
			debugPriceElements: () => Promise<unknown>
			selectTradingAsset: () => Promise<unknown>
		}
	}
}

export {}
