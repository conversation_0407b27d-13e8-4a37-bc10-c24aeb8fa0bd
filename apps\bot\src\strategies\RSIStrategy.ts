import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

export class RSIStrategy extends TradingStrategy {
	getName(): string {
		return 'RSI Strategy'
	}

	getDescription(): string {
		return 'Trades based on Relative Strength Index (RSI) overbought/oversold levels'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		const rsiPeriod = this.config.rsiPeriod || 14
		const oversoldLevel = this.config.oversoldLevel || 30
		const overboughtLevel = this.config.overboughtLevel || 70

		const rsi = this.calculateRSI(rsiPeriod)

		if (rsi === null) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for RSI calculation (need ${rsiPeriod + 1} data points)`
			}
		}

		// TODO: Implement proper RSI trading logic
		// For now, this is a placeholder implementation
		if (rsi <= oversoldLevel) {
			return {
				shouldTrade: true,
				direction: 'high',
				confidence: Math.min((oversoldLevel - rsi) / oversoldLevel, 1),
				reason: `RSI ${rsi.toFixed(2)} indicates oversold condition (< ${oversoldLevel})`
			}
		} else if (rsi >= overboughtLevel) {
			return {
				shouldTrade: true,
				direction: 'low',
				confidence: Math.min((rsi - overboughtLevel) / (100 - overboughtLevel), 1),
				reason: `RSI ${rsi.toFixed(2)} indicates overbought condition (> ${overboughtLevel})`
			}
		}

		return {
			shouldTrade: false,
			reason: `RSI ${rsi.toFixed(2)} in neutral zone (${oversoldLevel}-${overboughtLevel})`
		}
	}
}
