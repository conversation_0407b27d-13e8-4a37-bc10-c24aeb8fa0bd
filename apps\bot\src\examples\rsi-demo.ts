import { RSIStrategy } from '../strategies/RSIStrategy'
import { PriceData, StrategyConfig } from '../../../../shared/types'

/**
 * Demonstration of the improved RSI trading strategy
 */
async function demonstrateRSIStrategy() {
	console.log('=== RSI Strategy Demonstration ===\n')

	// Configure the RSI strategy with enhanced parameters
	const config: StrategyConfig = {
		rsiPeriod: 14,
		oversoldLevel: 30,
		overboughtLevel: 70,
		extremeOversoldLevel: 20,
		extremeOverboughtLevel: 80,
		enableDivergence: true,
		minConfidence: 0.6,
		trendConfirmation: true
	}

	const strategy = new RSIStrategy(config)

	console.log(`Strategy: ${strategy.getName()}`)
	console.log(`Description: ${strategy.getDescription()}\n`)

	// Simulate price data that creates different RSI conditions
	const scenarios = [
		{
			name: 'Normal Market Conditions',
			prices: generatePriceData(100, 20, 0.5) // Base price 100, 20 periods, low volatility
		},
		{
			name: 'Oversold Condition',
			prices: generateDecreasingPrices(100, 15, 2) // Declining prices to create oversold
		},
		{
			name: 'Overbought Condition',
			prices: generateIncreasingPrices(100, 15, 2) // Rising prices to create overbought
		},
		{
			name: 'High Volatility',
			prices: generatePriceData(100, 25, 3) // High volatility scenario
		}
	]

	for (const scenario of scenarios) {
		console.log(`\n--- ${scenario.name} ---`)
		
		// Reset strategy for each scenario
		const testStrategy = new RSIStrategy(config)
		
		// Process price data
		for (let i = 0; i < scenario.prices.length; i++) {
			const priceData = scenario.prices[i]
			const decision = await testStrategy.evaluate(priceData)
			
			// Log significant decisions
			if (decision.shouldTrade || i === scenario.prices.length - 1) {
				console.log(`Price: ${priceData.current.toFixed(2)} | ${decision.reason}`)
				if (decision.shouldTrade) {
					console.log(`  → TRADE: ${decision.direction?.toUpperCase()} (Confidence: ${decision.confidence?.toFixed(2)})`)
				}
			}
		}
	}

	console.log('\n=== Configuration Options ===')
	console.log('Available RSI Strategy Parameters:')
	console.log('• rsiPeriod: RSI calculation period (default: 14)')
	console.log('• oversoldLevel: Oversold threshold (default: 30)')
	console.log('• overboughtLevel: Overbought threshold (default: 70)')
	console.log('• extremeOversoldLevel: Extreme oversold threshold (default: 20)')
	console.log('• extremeOverboughtLevel: Extreme overbought threshold (default: 80)')
	console.log('• enableDivergence: Enable divergence detection (default: true)')
	console.log('• minConfidence: Minimum confidence for trades (default: 0.6)')
	console.log('• trendConfirmation: Enable trend confirmation (default: true)')
}

/**
 * Generate realistic price data with specified volatility
 */
function generatePriceData(basePrice: number, periods: number, volatility: number): PriceData[] {
	const prices: PriceData[] = []
	let currentPrice = basePrice
	
	for (let i = 0; i < periods; i++) {
		const previousPrice = currentPrice
		
		// Add random movement with specified volatility
		const change = (Math.random() - 0.5) * volatility
		currentPrice += change
		
		// Ensure price doesn't go negative
		currentPrice = Math.max(currentPrice, 1)
		
		const priceData: PriceData = {
			current: currentPrice,
			previous: previousPrice,
			timestamp: Date.now() + i * 60000, // 1 minute intervals
			trend: currentPrice > previousPrice ? 'up' : currentPrice < previousPrice ? 'down' : 'neutral',
			change: currentPrice - previousPrice,
			changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
		}
		
		prices.push(priceData)
	}
	
	return prices
}

/**
 * Generate decreasing prices to simulate oversold conditions
 */
function generateDecreasingPrices(basePrice: number, periods: number, declineRate: number): PriceData[] {
	const prices: PriceData[] = []
	let currentPrice = basePrice
	
	for (let i = 0; i < periods; i++) {
		const previousPrice = currentPrice
		
		// Gradual decline with some noise
		const decline = declineRate * (0.8 + Math.random() * 0.4) // 80-120% of decline rate
		currentPrice -= decline
		
		// Ensure price doesn't go negative
		currentPrice = Math.max(currentPrice, basePrice * 0.5)
		
		const priceData: PriceData = {
			current: currentPrice,
			previous: previousPrice,
			timestamp: Date.now() + i * 60000,
			trend: 'down',
			change: currentPrice - previousPrice,
			changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
		}
		
		prices.push(priceData)
	}
	
	return prices
}

/**
 * Generate increasing prices to simulate overbought conditions
 */
function generateIncreasingPrices(basePrice: number, periods: number, riseRate: number): PriceData[] {
	const prices: PriceData[] = []
	let currentPrice = basePrice
	
	for (let i = 0; i < periods; i++) {
		const previousPrice = currentPrice
		
		// Gradual rise with some noise
		const rise = riseRate * (0.8 + Math.random() * 0.4) // 80-120% of rise rate
		currentPrice += rise
		
		const priceData: PriceData = {
			current: currentPrice,
			previous: previousPrice,
			timestamp: Date.now() + i * 60000,
			trend: 'up',
			change: currentPrice - previousPrice,
			changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
		}
		
		prices.push(priceData)
	}
	
	return prices
}

// Run the demonstration
if (require.main === module) {
	demonstrateRSIStrategy().catch(console.error)
}

export { demonstrateRSIStrategy }
