'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
exports.TradingBot = void 0
const playwright_1 = require('playwright')
const events_1 = require('events')
class TradingBot extends events_1.EventEmitter {
	constructor(config) {
		super()
		this.browser = null
		this.page = null
		this.isRunning = false
		this.priceHistory = []
		this.trades = []
		this.priceCheckInterval = null
		this.config = config
		this.status = {
			isRunning: false,
			currentPrice: 0,
			tradesCount: 0,
			winCount: 0,
			lossCount: 0,
			winRate: 0,
			totalProfit: 0,
			accountBalance: 0,
			lastTrade: null
		}
	}
	async start() {
		if (this.isRunning) {
			throw new Error('Bot is already running')
		}
		try {
			this.log('info', 'Starting trading bot...')
			// Launch browser
			this.browser = await playwright_1.chromium.launch({
				headless: this.config.headless,
				userDataDir: this.config.userDataDir
			})
			this.page = await this.browser.newPage()
			// Navigate to Pocket Option
			await this.page.goto(this.config.pocketOptionUrl)
			// Wait for page to load
			await this.page.waitForLoadState('networkidle')
			this.isRunning = true
			this.status.isRunning = true
			this.status.startTime = Date.now()
			// Start price monitoring
			this.startPriceMonitoring()
			this.log('info', 'Trading bot started successfully')
			this.emitStatusUpdate()
		} catch (error) {
			this.log('error', `Failed to start bot: ${error}`)
			await this.stop()
			throw error
		}
	}
	async stop() {
		this.log('info', 'Stopping trading bot...')
		this.isRunning = false
		this.status.isRunning = false
		if (this.priceCheckInterval) {
			clearInterval(this.priceCheckInterval)
			this.priceCheckInterval = null
		}
		if (this.browser) {
			await this.browser.close()
			this.browser = null
			this.page = null
		}
		this.log('info', 'Trading bot stopped')
		this.emitStatusUpdate()
	}
	startPriceMonitoring() {
		this.priceCheckInterval = setInterval(async () => {
			try {
				await this.checkPrice()
			} catch (error) {
				this.log('error', `Price monitoring error: ${error}`)
			}
		}, 1000) // Check price every second
	}
	async checkPrice() {
		if (!this.page || !this.isRunning) return
		try {
			// Try to get price from DOM (this selector will need to be updated based on actual Pocket Option DOM)
			const priceElement = await this.page.$('.current-price, .price-value, [data-testid="price"]')
			if (!priceElement) {
				this.log('warn', 'Price element not found')
				return
			}
			const priceText = await priceElement.textContent()
			const currentPrice = parseFloat(priceText?.replace(/[^\d.-]/g, '') || '0')
			if (currentPrice > 0) {
				const previousPrice = this.status.currentPrice
				const priceData = {
					current: currentPrice,
					previous: previousPrice,
					timestamp: Date.now(),
					trend: currentPrice > previousPrice ? 'up' : currentPrice < previousPrice ? 'down' : 'neutral',
					change: currentPrice - previousPrice,
					changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
				}
				this.priceHistory.push(priceData)
				// Keep only last 100 price points
				if (this.priceHistory.length > 100) {
					this.priceHistory.shift()
				}
				this.status.currentPrice = currentPrice
				this.emit('price-update', priceData)
				// Check if we should make a trade
				if (this.config.settings.autoTrade) {
					await this.evaluateTradeOpportunity(priceData)
				}
			}
		} catch (error) {
			this.log('error', `Error checking price: ${error}`)
		}
	}
	async evaluateTradeOpportunity(priceData) {
		// Simple trading logic based on price change threshold
		const { threshold } = this.config.settings
		// Check if price change exceeds threshold
		if (Math.abs(priceData.changePercent) >= threshold) {
			const direction = priceData.trend === 'up' ? 'high' : 'low'
			await this.executeTrade(direction, priceData)
		}
	}
	async executeTrade(direction, priceData) {
		if (!this.page) return
		try {
			this.log('info', `Executing ${direction} trade at price ${priceData.current}`)
			// Find and click the appropriate button (selectors need to be updated for actual Pocket Option)
			const buttonSelector = direction === 'high' ? '.btn-call, .high-btn' : '.btn-put, .low-btn'
			const button = await this.page.$(buttonSelector)
			if (!button) {
				this.log('error', `${direction} button not found`)
				return
			}
			await button.click()
			// Create trade record
			const trade = {
				id: `trade_${Date.now()}`,
				timestamp: Date.now(),
				direction,
				amount: this.config.settings.tradeAmount,
				entryPrice: priceData.current,
				result: 'pending',
				duration: 60 // Default 1 minute trade
			}
			this.trades.push(trade)
			this.status.tradesCount++
			this.status.lastTrade = trade
			this.emit('trade-result', trade)
			this.emitStatusUpdate()
			// Wait for trade result (simplified - in real implementation, monitor DOM for result)
			setTimeout(() => {
				this.checkTradeResult(trade.id)
			}, trade.duration * 1000)
		} catch (error) {
			this.log('error', `Error executing trade: ${error}`)
		}
	}
	async checkTradeResult(tradeId) {
		// This is a simplified implementation
		// In reality, you'd monitor the DOM for trade results
		const trade = this.trades.find(t => t.id === tradeId)
		if (!trade) return
		// Simulate random result for now (replace with actual DOM monitoring)
		const isWin = Math.random() > 0.5
		trade.result = isWin ? 'win' : 'loss'
		trade.exitPrice = this.status.currentPrice
		trade.profit = isWin ? trade.amount * 0.8 : -trade.amount // 80% payout
		if (isWin) {
			this.status.winCount++
		} else {
			this.status.lossCount++
		}
		this.status.winRate = (this.status.winCount / this.status.tradesCount) * 100
		this.status.totalProfit += trade.profit
		this.status.lastTrade = trade
		this.emit('trade-result', trade)
		this.emitStatusUpdate()
		this.log('info', `Trade ${tradeId} result: ${trade.result}, profit: ${trade.profit}`)
	}
	emitStatusUpdate() {
		this.status.uptime = this.status.startTime ? Date.now() - this.status.startTime : 0
		this.emit('status-update', { ...this.status })
	}
	log(level, message) {
		const logEntry = {
			level,
			message,
			timestamp: Date.now()
		}
		this.emit('log', logEntry)
		console.log(`[${level.toUpperCase()}] ${message}`)
	}
	getStatus() {
		return { ...this.status }
	}
	updateSettings(settings) {
		this.config.settings = { ...settings }
		this.log('info', 'Settings updated')
	}
}
exports.TradingBot = TradingBot
//# sourceMappingURL=TradingBot.js.map
