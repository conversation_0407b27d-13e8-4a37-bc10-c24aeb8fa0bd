import type { BotStatus, PriceData, TradeResult } from '../../../../shared/types'

interface TradingDashboardProps {
	botStatus: BotStatus
	currentPrice: PriceData | null
	recentTrades: TradeResult[]
	onStartBot: () => void
	onStopBot: () => void
	statusMessage?: string
	browserStatus: { initialized: boolean; loggedIn: boolean }
	onInitializeBrowser: () => void
	onCheckLoginStatus: () => void
	onCloseBrowser: () => void

	// TODO: Remove this on production
	onDebugPriceElements: () => void
	onSelectTradingAsset: () => void
}

export function TradingDashboard({
	botStatus,
	currentPrice,
	recentTrades,
	onStartBot,
	onStopBot,
	statusMessage,
	browserStatus,
	onInitializeBrowser,
	onCheckLoginStatus,
	onCloseBrowser,

	// TODO: Remove this on production
	onDebugPriceElements,
	onSelectTradingAsset
}: TradingDashboardProps) {
	const formatPrice = (price: number) => price.toFixed(5)
	const formatPercent = (percent: number) => `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`
	const formatProfit = (profit: number) => `${profit >= 0 ? '+' : ''}$${profit.toFixed(2)}`
	const formatUptime = (uptime?: number) => {
		if (!uptime) return '00:00:00'
		const hours = Math.floor(uptime / 3600000)
		const minutes = Math.floor((uptime % 3600000) / 60000)
		const seconds = Math.floor((uptime % 60000) / 1000)
		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
			.toString()
			.padStart(2, '0')}`
	}

	const getTrendColor = (trend?: string) => {
		switch (trend) {
			case 'up':
				return 'text-green-400'
			case 'down':
				return 'text-red-400'
			default:
				return 'text-gray-400'
		}
	}

	const getTrendIcon = (trend?: string) => {
		switch (trend) {
			case 'up':
				return '↗'
			case 'down':
				return '↘'
			default:
				return '→'
		}
	}

	return (
		<div className="space-y-4">
			{/* Browser Control Panel */}
			<div className="bg-gray-800 rounded-lg p-4">
				<div className="flex items-center justify-between mb-3">
					<h2 className="text-lg font-semibold">Browser Control</h2>
					<div className="flex gap-2">
						<div
							className={`px-2 py-1 rounded-full text-xs font-medium ${
								browserStatus.initialized ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
							}`}
						>
							{browserStatus.initialized ? 'Ready' : 'Not Ready'}
						</div>
						{browserStatus.initialized && (
							<div
								className={`px-2 py-1 rounded-full text-xs font-medium ${
									browserStatus.loggedIn ? 'bg-blue-900 text-blue-300' : 'bg-yellow-900 text-yellow-300'
								}`}
							>
								{browserStatus.loggedIn ? 'Logged In' : 'Not Logged In'}
							</div>
						)}
					</div>
				</div>

				<div className="flex gap-3 flex-wrap">
					{!browserStatus.initialized ? (
						<button
							onClick={onInitializeBrowser}
							className="px-4 py-2 rounded-lg font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white text-sm"
						>
							Initialize Browser
						</button>
					) : (
						<>
							<button
								onClick={onCheckLoginStatus}
								className="px-4 py-2 rounded-lg font-medium transition-colors bg-yellow-600 hover:bg-yellow-700 text-white text-sm"
							>
								Check Login
							</button>
							<button
								onClick={onCloseBrowser}
								className="px-4 py-2 rounded-lg font-medium transition-colors bg-red-600 hover:bg-red-700 text-white text-sm"
							>
								Close Browser
							</button>
						</>
					)}
				</div>

				{browserStatus.initialized && !browserStatus.loggedIn && (
					<div className="mt-3 p-3 bg-yellow-900 bg-opacity-50 rounded-lg border border-yellow-600">
						<p className="text-yellow-300 text-xs">
							⚠️ Please log in to your Pocket Option account in the browser window before starting the bot.
						</p>
					</div>
				)}
			</div>

			{/* Bot Control Panel */}
			<div className="bg-gray-800 rounded-lg p-4">
				<div className="flex items-center justify-between mb-3">
					<h2 className="text-lg font-semibold">Bot Control</h2>
					<div
						className={`px-2 py-1 rounded-full text-xs font-medium ${
							botStatus.isRunning ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
						}`}
					>
						{botStatus.isRunning ? 'Running' : 'Stopped'}
					</div>
				</div>

				<div className="flex gap-3">
					<button
						onClick={onStartBot}
						disabled={botStatus.isRunning || !browserStatus.initialized || !browserStatus.loggedIn}
						className={`px-4 py-2 rounded-lg font-medium transition-colors text-sm ${
							botStatus.isRunning || !browserStatus.initialized || !browserStatus.loggedIn
								? 'bg-gray-600 text-gray-400 cursor-not-allowed'
								: 'bg-green-600 hover:bg-green-700 text-white'
						}`}
					>
						Start Bot
					</button>

					<button
						onClick={onStopBot}
						disabled={!botStatus.isRunning}
						className={`px-4 py-2 rounded-lg font-medium transition-colors text-sm ${
							!botStatus.isRunning
								? 'bg-gray-600 text-gray-400 cursor-not-allowed'
								: 'bg-red-600 hover:bg-red-700 text-white'
						}`}
					>
						Stop Bot
					</button>
				</div>

				{(!browserStatus.initialized || !browserStatus.loggedIn) && (
					<div className="mt-3 p-3 bg-blue-900 bg-opacity-50 rounded-lg border border-blue-600">
						<p className="text-blue-300 text-xs">
							ℹ️{' '}
							{!browserStatus.initialized
								? 'Initialize the browser first, then log in to Pocket Option to enable bot trading.'
								: 'Please log in to Pocket Option to enable bot trading.'}
						</p>
					</div>
				)}
			</div>

			{/* TODO: Remove this block on production */}
			<div className="bg-gray-800 rounded-lg p-4">
				<h2 className="text-lg font-semibold mb-3">Bot Debugger</h2>
				<div className="flex gap-3 flex-wrap">
					<button
						onClick={onDebugPriceElements}
						className="px-4 py-2 rounded-lg font-medium transition-colors bg-purple-600 hover:bg-purple-700 text-white text-sm"
					>
						Debug Price Elements
					</button>
					<button
						onClick={onSelectTradingAsset}
						className="px-4 py-2 rounded-lg font-medium transition-colors bg-indigo-600 hover:bg-indigo-700 text-white text-sm"
					>
						Select Trading Asset
					</button>
				</div>
			</div>

			{/* Status Message */}
			{statusMessage && (
				<div className="bg-blue-900 border border-blue-700 rounded-lg p-3">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<svg className="h-4 w-4 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
								<path
									fillRule="evenodd"
									d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
									clipRule="evenodd"
								/>
							</svg>
						</div>
						<div className="ml-2">
							<p className="text-xs text-blue-300">{statusMessage}</p>
						</div>
					</div>
				</div>
			)}

			{/* Price Display */}
			<div className="bg-gray-800 rounded-lg p-4">
				<h2 className="text-lg font-semibold mb-3">Current Price</h2>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-3">
					<div className="text-center">
						<div className="text-2xl font-bold text-blue-400">
							{currentPrice ? formatPrice(currentPrice.current) : formatPrice(botStatus.currentPrice)}
						</div>
						<div className="text-gray-400 text-sm">Current Price</div>
					</div>

					{currentPrice && (
						<>
							<div className="text-center">
								<div className={`text-xl font-bold ${getTrendColor(currentPrice.trend)}`}>
									{getTrendIcon(currentPrice.trend)} {formatPercent(currentPrice.changePercent)}
								</div>
								<div className="text-gray-400 text-sm">Change</div>
							</div>

							<div className="text-center">
								<div className={`text-xl font-bold ${getTrendColor(currentPrice.trend)}`}>
									{formatPrice(Math.abs(currentPrice.change))}
								</div>
								<div className="text-gray-400 text-sm">Price Change</div>
							</div>
						</>
					)}
				</div>
			</div>

			{/* Statistics */}
			<div className="grid grid-cols-2 lg:grid-cols-5 gap-3">
				<div className="bg-gray-800 rounded-lg p-3">
					<div className="text-xl font-bold text-yellow-400">${botStatus.accountBalance.toFixed(2)}</div>
					<div className="text-gray-400 text-sm">Account Balance</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-3">
					<div className="text-xl font-bold text-blue-400">{botStatus.tradesCount}</div>
					<div className="text-gray-400 text-sm">Total Trades</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-3">
					<div className="text-xl font-bold text-green-400">{botStatus.winRate.toFixed(1)}%</div>
					<div className="text-gray-400 text-sm">Win Rate</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-3">
					<div className={`text-xl font-bold ${botStatus.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
						{formatProfit(botStatus.totalProfit)}
					</div>
					<div className="text-gray-400 text-sm">Total Profit</div>
				</div>

				<div className="bg-gray-800 rounded-lg p-3">
					<div className="text-xl font-bold text-purple-400">{formatUptime(botStatus.uptime)}</div>
					<div className="text-gray-400 text-sm">Uptime</div>
				</div>
			</div>

			{/* Recent Trades */}
			<div className="bg-gray-800 rounded-lg p-4">
				<h2 className="text-lg font-semibold mb-3">Recent Trades</h2>
				{recentTrades.length === 0 ? (
					<div className="text-gray-400 text-center py-4 text-sm">No trades yet</div>
				) : (
					<div className="overflow-x-auto">
						<table className="w-full text-xs">
							<thead>
								<tr className="border-b border-gray-700">
									<th className="text-left py-1">Time</th>
									<th className="text-left py-1">Direction</th>
									<th className="text-left py-1">Entry Price</th>
									<th className="text-left py-1">Exit Price</th>
									<th className="text-left py-1">Result</th>
									<th className="text-left py-1">Profit</th>
								</tr>
							</thead>
							<tbody>
								{recentTrades.map(trade => (
									<tr key={trade.id} className="border-b border-gray-700">
										<td className="py-1">{new Date(trade.timestamp).toLocaleTimeString()}</td>
										<td className="py-1">
											<span
												className={`px-1 py-0.5 rounded text-xs ${
													trade.direction === 'high' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
												}`}
											>
												{trade.direction.toUpperCase()}
											</span>
										</td>
										<td className="py-1">{formatPrice(trade.entryPrice)}</td>
										<td className="py-1">{trade.exitPrice ? formatPrice(trade.exitPrice) : '-'}</td>
										<td className="py-1">
											<span
												className={`px-1 py-0.5 rounded text-xs ${
													trade.result === 'win'
														? 'bg-green-900 text-green-300'
														: trade.result === 'loss'
														? 'bg-red-900 text-red-300'
														: 'bg-yellow-900 text-yellow-300'
												}`}
											>
												{trade.result.toUpperCase()}
											</span>
										</td>
										<td className={`py-1 ${trade.profit && trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
											{trade.profit ? formatProfit(trade.profit) : '-'}
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				)}
			</div>
		</div>
	)
}
