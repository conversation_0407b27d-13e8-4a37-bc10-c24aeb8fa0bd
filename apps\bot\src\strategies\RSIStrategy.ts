import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface RSIAnalysis {
	current: number
	previous: number
	trend: 'rising' | 'falling' | 'neutral'
	momentum: 'strong' | 'weak' | 'neutral'
	zone: 'extreme_oversold' | 'oversold' | 'neutral' | 'overbought' | 'extreme_overbought'
}

interface DivergenceSignal {
	type: 'bullish' | 'bearish' | 'none'
	strength: number // 0-1
	periods: number
}

export class RSIStrategy extends TradingStrategy {
	private lastTradeTime: number = 0
	private rsiHistory: number[] = []
	private readonly COOLDOWN_PERIOD = 60000 // 1 minute cooldown between trades

	getName(): string {
		return 'RSI Strategy'
	}

	getDescription(): string {
		return 'Advanced RSI strategy with divergence detection, trend confirmation, and risk management'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with enhanced defaults
		const rsiPeriod = this.config.rsiPeriod || 14
		const oversoldLevel = this.config.oversoldLevel || 30
		const overboughtLevel = this.config.overboughtLevel || 70
		const extremeOversoldLevel = this.config.extremeOversoldLevel || 20
		const extremeOverboughtLevel = this.config.extremeOverboughtLevel || 80
		const enableDivergence = this.config.enableDivergence !== false // Default true
		const minConfidence = this.config.minConfidence || 0.6
		const trendConfirmation = this.config.trendConfirmation !== false // Default true

		// Calculate RSI using improved method
		const rsi = this.calculateWildersRSI(rsiPeriod)

		if (rsi === null) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for RSI calculation (need ${rsiPeriod + 1} data points)`
			}
		}

		// Update RSI history
		this.rsiHistory.push(rsi)
		if (this.rsiHistory.length > 50) {
			this.rsiHistory = this.rsiHistory.slice(-50)
		}

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		// Perform comprehensive RSI analysis
		const analysis = this.analyzeRSI(rsi)
		const divergence = enableDivergence ? this.detectDivergence() : { type: 'none' as const, strength: 0, periods: 0 }
		const trendConfirmed = trendConfirmation ? this.confirmTrend(priceData) : true

		// Generate trading decision
		const decision = this.generateTradingDecision(
			analysis,
			divergence,
			trendConfirmed,
			oversoldLevel,
			overboughtLevel,
			extremeOversoldLevel,
			extremeOverboughtLevel,
			minConfidence
		)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
		}

		return decision
	}

	/**
	 * Calculate RSI using Wilder's exponential smoothing method
	 */
	private calculateWildersRSI(period: number): number | null {
		if (this.priceHistory.length < period + 1) {
			return null
		}

		const prices = this.priceHistory.slice(-(period + 1)).map(p => p.current)

		// Calculate initial gains and losses
		let gains: number[] = []
		let losses: number[] = []

		for (let i = 1; i < prices.length; i++) {
			const change = prices[i] - prices[i - 1]
			gains.push(change > 0 ? change : 0)
			losses.push(change < 0 ? Math.abs(change) : 0)
		}

		// Calculate initial averages
		let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period
		let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period

		// Apply Wilder's smoothing for remaining periods
		for (let i = period; i < gains.length; i++) {
			avgGain = (avgGain * (period - 1) + gains[i]) / period
			avgLoss = (avgLoss * (period - 1) + losses[i]) / period
		}

		if (avgLoss === 0) return 100

		const rs = avgGain / avgLoss
		return 100 - 100 / (1 + rs)
	}

	/**
	 * Analyze RSI characteristics
	 */
	private analyzeRSI(currentRSI: number): RSIAnalysis {
		const previousRSI = this.rsiHistory.length > 1 ? this.rsiHistory[this.rsiHistory.length - 2] : currentRSI

		// Determine trend
		let trend: 'rising' | 'falling' | 'neutral' = 'neutral'
		if (currentRSI > previousRSI + 1) trend = 'rising'
		else if (currentRSI < previousRSI - 1) trend = 'falling'

		// Determine momentum
		const rsiChange = Math.abs(currentRSI - previousRSI)
		let momentum: 'strong' | 'weak' | 'neutral' = 'neutral'
		if (rsiChange > 5) momentum = 'strong'
		else if (rsiChange > 2) momentum = 'weak'

		// Determine zone
		let zone: RSIAnalysis['zone'] = 'neutral'
		if (currentRSI <= 20) zone = 'extreme_oversold'
		else if (currentRSI <= 30) zone = 'oversold'
		else if (currentRSI >= 80) zone = 'extreme_overbought'
		else if (currentRSI >= 70) zone = 'overbought'

		return {
			current: currentRSI,
			previous: previousRSI,
			trend,
			momentum,
			zone
		}
	}

	/**
	 * Detect RSI divergence signals
	 */
	private detectDivergence(): DivergenceSignal {
		if (this.rsiHistory.length < 10 || this.priceHistory.length < 10) {
			return { type: 'none', strength: 0, periods: 0 }
		}

		const lookbackPeriods = Math.min(20, this.rsiHistory.length - 1)
		const recentRSI = this.rsiHistory.slice(-lookbackPeriods)
		const recentPrices = this.priceHistory.slice(-lookbackPeriods).map(p => p.current)

		// Find RSI and price peaks/troughs
		const rsiPeaks = this.findPeaks(recentRSI)
		const rsiBottoms = this.findBottoms(recentRSI)
		const pricePeaks = this.findPeaks(recentPrices)
		const priceBottoms = this.findBottoms(recentPrices)

		// Check for bullish divergence (price makes lower lows, RSI makes higher lows)
		if (rsiBottoms.length >= 2 && priceBottoms.length >= 2) {
			const lastRSIBottom = rsiBottoms[rsiBottoms.length - 1]
			const prevRSIBottom = rsiBottoms[rsiBottoms.length - 2]
			const lastPriceBottom = priceBottoms[priceBottoms.length - 1]
			const prevPriceBottom = priceBottoms[priceBottoms.length - 2]

			if (
				recentRSI[lastRSIBottom.index] > recentRSI[prevRSIBottom.index] &&
				recentPrices[lastPriceBottom.index] < recentPrices[prevPriceBottom.index]
			) {
				const strength = this.calculateDivergenceStrength(
					recentRSI[lastRSIBottom.index] - recentRSI[prevRSIBottom.index],
					recentPrices[prevPriceBottom.index] - recentPrices[lastPriceBottom.index]
				)
				return { type: 'bullish', strength, periods: lastRSIBottom.index - prevRSIBottom.index }
			}
		}

		// Check for bearish divergence (price makes higher highs, RSI makes lower highs)
		if (rsiPeaks.length >= 2 && pricePeaks.length >= 2) {
			const lastRSIPeak = rsiPeaks[rsiPeaks.length - 1]
			const prevRSIPeak = rsiPeaks[rsiPeaks.length - 2]
			const lastPricePeak = pricePeaks[pricePeaks.length - 1]
			const prevPricePeak = pricePeaks[pricePeaks.length - 2]

			if (
				recentRSI[lastRSIPeak.index] < recentRSI[prevRSIPeak.index] &&
				recentPrices[lastPricePeak.index] > recentPrices[prevPricePeak.index]
			) {
				const strength = this.calculateDivergenceStrength(
					recentRSI[prevRSIPeak.index] - recentRSI[lastRSIPeak.index],
					recentPrices[lastPricePeak.index] - recentPrices[prevPricePeak.index]
				)
				return { type: 'bearish', strength, periods: lastRSIPeak.index - prevRSIPeak.index }
			}
		}

		return { type: 'none', strength: 0, periods: 0 }
	}

	/**
	 * Find peaks in a data series
	 */
	private findPeaks(data: number[]): Array<{ index: number; value: number }> {
		const peaks: Array<{ index: number; value: number }> = []

		for (let i = 1; i < data.length - 1; i++) {
			if (data[i] > data[i - 1] && data[i] > data[i + 1]) {
				peaks.push({ index: i, value: data[i] })
			}
		}

		return peaks
	}

	/**
	 * Find bottoms in a data series
	 */
	private findBottoms(data: number[]): Array<{ index: number; value: number }> {
		const bottoms: Array<{ index: number; value: number }> = []

		for (let i = 1; i < data.length - 1; i++) {
			if (data[i] < data[i - 1] && data[i] < data[i + 1]) {
				bottoms.push({ index: i, value: data[i] })
			}
		}

		return bottoms
	}

	/**
	 * Calculate divergence strength
	 */
	private calculateDivergenceStrength(rsiDiff: number, priceDiff: number): number {
		const normalizedRSIDiff = Math.abs(rsiDiff) / 100
		const normalizedPriceDiff = Math.abs(priceDiff) / Math.max(...this.priceHistory.slice(-20).map(p => p.current))
		return Math.min((normalizedRSIDiff + normalizedPriceDiff) / 2, 1)
	}

	/**
	 * Confirm trend using price action
	 */
	private confirmTrend(priceData: PriceData): boolean {
		if (this.priceHistory.length < 5) return true

		const recentPrices = this.priceHistory.slice(-5).map(p => p.current)
		const shortMA = recentPrices.reduce((sum, price) => sum + price, 0) / recentPrices.length
		const currentPrice = priceData.current

		// Simple trend confirmation: current price vs short-term average
		const trendStrength = Math.abs(currentPrice - shortMA) / shortMA
		return trendStrength > 0.001 // 0.1% minimum trend strength
	}

	/**
	 * Generate comprehensive trading decision
	 */
	private generateTradingDecision(
		analysis: RSIAnalysis,
		divergence: DivergenceSignal,
		trendConfirmed: boolean,
		oversoldLevel: number,
		overboughtLevel: number,
		extremeOversoldLevel: number,
		extremeOverboughtLevel: number,
		minConfidence: number
	): TradingDecision {
		let baseConfidence = 0
		let direction: 'high' | 'low' | undefined
		let reason = ''

		// Base RSI signal evaluation
		if (analysis.zone === 'extreme_oversold') {
			direction = 'high'
			baseConfidence = 0.8
			reason = `RSI ${analysis.current.toFixed(2)} in extreme oversold zone (< ${extremeOversoldLevel})`
		} else if (analysis.zone === 'oversold') {
			direction = 'high'
			baseConfidence = 0.6
			reason = `RSI ${analysis.current.toFixed(2)} in oversold zone (< ${oversoldLevel})`
		} else if (analysis.zone === 'extreme_overbought') {
			direction = 'low'
			baseConfidence = 0.8
			reason = `RSI ${analysis.current.toFixed(2)} in extreme overbought zone (> ${extremeOverboughtLevel})`
		} else if (analysis.zone === 'overbought') {
			direction = 'low'
			baseConfidence = 0.6
			reason = `RSI ${analysis.current.toFixed(2)} in overbought zone (> ${overboughtLevel})`
		}

		// Apply modifiers
		let finalConfidence = baseConfidence

		// RSI momentum modifier
		if (analysis.momentum === 'strong') {
			finalConfidence += 0.1
			reason += ` with strong momentum`
		} else if (analysis.momentum === 'weak') {
			finalConfidence -= 0.1
			reason += ` with weak momentum`
		}

		// RSI trend modifier
		if (direction === 'high' && analysis.trend === 'rising') {
			finalConfidence += 0.1
			reason += ` and rising RSI`
		} else if (direction === 'low' && analysis.trend === 'falling') {
			finalConfidence += 0.1
			reason += ` and falling RSI`
		} else if (direction === 'high' && analysis.trend === 'falling') {
			finalConfidence -= 0.05
			reason += ` but RSI still falling`
		} else if (direction === 'low' && analysis.trend === 'rising') {
			finalConfidence -= 0.05
			reason += ` but RSI still rising`
		}

		// Divergence modifier
		if (divergence.type === 'bullish' && direction === 'high') {
			finalConfidence += divergence.strength * 0.2
			reason += ` with bullish divergence (strength: ${divergence.strength.toFixed(2)})`
		} else if (divergence.type === 'bearish' && direction === 'low') {
			finalConfidence += divergence.strength * 0.2
			reason += ` with bearish divergence (strength: ${divergence.strength.toFixed(2)})`
		} else if (divergence.type === 'bullish' && direction === 'low') {
			finalConfidence -= 0.2
			reason += ` but conflicting bullish divergence detected`
		} else if (divergence.type === 'bearish' && direction === 'high') {
			finalConfidence -= 0.2
			reason += ` but conflicting bearish divergence detected`
		}

		// Trend confirmation modifier
		if (!trendConfirmed) {
			finalConfidence -= 0.15
			reason += ` with weak trend confirmation`
		}

		// Ensure confidence is within bounds
		finalConfidence = Math.max(0, Math.min(1, finalConfidence))

		// Final decision
		const shouldTrade = finalConfidence >= minConfidence && direction !== undefined

		if (!shouldTrade) {
			if (direction === undefined) {
				return {
					shouldTrade: false,
					reason: `RSI ${analysis.current.toFixed(2)} in neutral zone (${oversoldLevel}-${overboughtLevel})`
				}
			} else {
				return {
					shouldTrade: false,
					reason: `${reason} - confidence ${finalConfidence.toFixed(2)} below minimum ${minConfidence}`
				}
			}
		}

		return {
			shouldTrade: true,
			direction,
			confidence: finalConfidence,
			reason: `${reason} - confidence: ${finalConfidence.toFixed(2)}`
		}
	}
}
