import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

export class MovingAverageStrategy extends TradingStrategy {
	getName(): string {
		return 'Moving Average Crossover'
	}

	getDescription(): string {
		return 'Trades based on short-term and long-term moving average crossovers'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		const shortPeriod = this.config.shortPeriod || 5
		const longPeriod = this.config.longPeriod || 20

		const shortMA = this.getSimpleMovingAverage(shortPeriod)
		const longMA = this.getSimpleMovingAverage(longPeriod)

		if (shortMA === null || longMA === null) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for moving averages (need ${longPeriod} data points)`
			}
		}

		// TODO: Implement moving average crossover logic
		// For now, this is a placeholder implementation
		const currentPrice = priceData.current
		
		// Simple logic: trade high if price is above both MAs, low if below both
		if (currentPrice > shortMA && currentPrice > longMA && shortMA > longMA) {
			return {
				shouldTrade: true,
				direction: 'high',
				confidence: 0.7,
				reason: `Price ${currentPrice} above both MAs (Short: ${shortMA.toFixed(4)}, Long: ${longMA.toFixed(4)})`
			}
		} else if (currentPrice < shortMA && currentPrice < longMA && shortMA < longMA) {
			return {
				shouldTrade: true,
				direction: 'low',
				confidence: 0.7,
				reason: `Price ${currentPrice} below both MAs (Short: ${shortMA.toFixed(4)}, Long: ${longMA.toFixed(4)})`
			}
		}

		return {
			shouldTrade: false,
			reason: `No clear trend signal from moving averages`
		}
	}
}
