import { PriceData, StrategyConfig } from '../../../../shared/types'

export interface TradingDecision {
	shouldTrade: boolean
	direction?: 'high' | 'low'
	confidence?: number // 0-1 scale
	reason?: string
}

export abstract class TradingStrategy {
	protected config: StrategyConfig
	protected priceHistory: PriceData[] = []

	constructor(config: StrategyConfig) {
		this.config = config
	}

	abstract getName(): string
	abstract getDescription(): string
	abstract evaluate(priceData: PriceData): Promise<TradingDecision>

	// Update strategy configuration
	updateConfig(config: StrategyConfig): void {
		this.config = config
	}

	// Add price data to history for technical analysis
	addPriceData(priceData: PriceData): void {
		this.priceHistory.push(priceData)

		// Keep only last 200 data points to prevent memory issues
		if (this.priceHistory.length > 200) {
			this.priceHistory = this.priceHistory.slice(-200)
		}
	}

	// Helper method to get simple moving average
	protected getSimpleMovingAverage(period: number): number | null {
		if (this.priceHistory.length < period) {
			return null
		}

		const prices = this.priceHistory.slice(-period).map(p => p.current)
		const sum = prices.reduce((acc, price) => acc + price, 0)
		return sum / period
	}

	// Helper method to calculate RSI (simple method for backward compatibility)
	protected calculateRSI(period: number = 14): number | null {
		if (this.priceHistory.length < period + 1) {
			return null
		}

		const prices = this.priceHistory.slice(-(period + 1)).map(p => p.current)
		let gains = 0
		let losses = 0

		for (let i = 1; i < prices.length; i++) {
			const change = prices[i] - prices[i - 1]
			if (change > 0) {
				gains += change
			} else {
				losses += Math.abs(change)
			}
		}

		const avgGain = gains / period
		const avgLoss = losses / period

		if (avgLoss === 0) return 100

		const rs = avgGain / avgLoss
		return 100 - 100 / (1 + rs)
	}

	// Helper method to calculate RSI using Wilder's exponential smoothing method (more accurate)
	protected calculateWildersRSI(period: number = 14): number | null {
		if (this.priceHistory.length < period + 1) {
			return null
		}

		const prices = this.priceHistory.slice(-(period + 1)).map(p => p.current)

		// Calculate initial gains and losses
		let gains: number[] = []
		let losses: number[] = []

		for (let i = 1; i < prices.length; i++) {
			const change = prices[i] - prices[i - 1]
			gains.push(change > 0 ? change : 0)
			losses.push(change < 0 ? Math.abs(change) : 0)
		}

		// Calculate initial averages
		let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period
		let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period

		// Apply Wilder's smoothing for remaining periods
		for (let i = period; i < gains.length; i++) {
			avgGain = (avgGain * (period - 1) + gains[i]) / period
			avgLoss = (avgLoss * (period - 1) + losses[i]) / period
		}

		if (avgLoss === 0) return 100

		const rs = avgGain / avgLoss
		return 100 - 100 / (1 + rs)
	}

	// Helper method to calculate standard deviation
	protected getStandardDeviation(values: number[]): number {
		const mean = values.reduce((sum, val) => sum + val, 0) / values.length
		const squaredDiffs = values.map(val => Math.pow(val - mean, 2))
		const avgSquaredDiff = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length
		return Math.sqrt(avgSquaredDiff)
	}
}
