// Trading related types
export type StrategyType = 'threshold' | 'moving-average' | 'rsi' | 'macd' | 'bollinger-bands' | 'custom'

export interface StrategyConfig {
	// Threshold Strategy
	threshold?: number

	// Moving Average Strategy
	shortPeriod?: number
	longPeriod?: number

	// RSI Strategy
	rsiPeriod?: number
	oversoldLevel?: number
	overboughtLevel?: number
	extremeOversoldLevel?: number
	extremeOverboughtLevel?: number
	enableDivergence?: boolean
	minConfidence?: number
	trendConfirmation?: boolean

	// MACD Strategy
	fastPeriod?: number
	slowPeriod?: number
	signalPeriod?: number

	// Bollinger Bands Strategy
	period?: number
	standardDeviations?: number

	// Custom Strategy
	customCode?: string
}

export type StopLossMode = 'percentage' | 'fixed'

export interface StopLossConfig {
	enabled: boolean
	mode: StopLossMode
	percentage?: number // Stop loss as percentage of total balance
	fixedAmount?: number // Stop loss as fixed dollar amount
}

export interface TradingSettings {
	strategy: StrategyType
	strategyConfig: StrategyConfig
	tradeAmount: number
	autoTrade: boolean
	stopLoss?: StopLossConfig
	takeProfit?: number
	assetType?: 'currency' | 'cryptocurrency' | 'commodity' | 'stock' | 'index'
	asset?: string
	assetFilter?: 'OTC' | 'ALL'
}

export interface PriceData {
	current: number
	previous: number
	timestamp: number
	trend: 'up' | 'down' | 'neutral'
	change: number
	changePercent: number
}

export interface TradeResult {
	id: string
	timestamp: number
	direction: 'high' | 'low'
	amount: number
	entryPrice: number
	exitPrice?: number
	result: 'win' | 'loss' | 'pending'
	profit?: number
	duration: number
}

export interface BotStatus {
	isRunning: boolean
	currentPrice: number
	tradesCount: number
	winCount: number
	lossCount: number
	winRate: number
	totalProfit: number
	accountBalance: number
	lastTrade: TradeResult | null
	startTime?: number
	uptime?: number
}

export interface BotConfig {
	settings: TradingSettings
	pocketOptionUrl: string
	headless: boolean
	userDataDir?: string
	existingBrowser?: any
	existingPage?: any
}

// Event types for IPC communication
export interface BotEvents {
	'status-update': BotStatus
	'price-update': PriceData
	'trade-result': TradeResult
	'stop-loss-triggered': { totalProfit: number; stopLossConfig?: StopLossConfig }
	error: { message: string; details?: any }
	log: { level: 'info' | 'warn' | 'error'; message: string; timestamp: number }
}

// API Response types
export interface ApiResponse<T = any> {
	success: boolean
	data?: T
	message?: string
	error?: string
}
